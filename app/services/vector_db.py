"""
Vector database service for SCONIA using Qdrant.
Handles embedding storage, retrieval, and semantic search operations.
"""
from typing import List, Dict, Any, Optional, Tuple
import logging
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
import numpy as np
import uuid
from datetime import datetime

from app.config import settings

logger = logging.getLogger(__name__)


class VectorDBService:
    """Service for managing vector database operations with Qdrant."""
    
    def __init__(self):
        """Initialize Qdrant client and collections."""
        self.client = QdrantClient(
            url=settings.qdrant_url,
            api_key=settings.qdrant_api_key,
            timeout=30
        )
        self.collection_name = "sconia_legal_documents"
        self.embedding_size = 1536  # OpenAI text-embedding-ada-002 dimension
        
    async def initialize_collections(self):
        """Initialize Qdrant collections for legal documents."""
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                # Create collection with vector configuration
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.embedding_size,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created collection: {self.collection_name}")
            else:
                logger.info(f"Collection {self.collection_name} already exists")
                
            # Create indexes for better performance
            await self._create_indexes()
            
        except Exception as e:
            logger.error(f"Error initializing collections: {e}")
            raise
    
    async def _create_indexes(self):
        """Create indexes for efficient filtering."""
        try:
            # Create payload indexes for filtering
            self.client.create_payload_index(
                collection_name=self.collection_name,
                field_name="document_type",
                field_schema=models.KeywordIndexParams()
            )
            
            self.client.create_payload_index(
                collection_name=self.collection_name,
                field_name="document_id",
                field_schema=models.KeywordIndexParams()
            )
            
            self.client.create_payload_index(
                collection_name=self.collection_name,
                field_name="created_at",
                field_schema=models.IntegerIndexParams()
            )
            
            logger.info("Created payload indexes")
            
        except Exception as e:
            logger.warning(f"Error creating indexes (may already exist): {e}")
    
    async def store_embeddings(
        self,
        embeddings: List[List[float]],
        texts: List[str],
        metadata_list: List[Dict[str, Any]]
    ) -> List[str]:
        """
        Store embeddings with associated text and metadata.
        
        Args:
            embeddings: List of embedding vectors
            texts: List of text chunks
            metadata_list: List of metadata dictionaries
            
        Returns:
            List of point IDs
        """
        try:
            points = []
            point_ids = []
            
            for i, (embedding, text, metadata) in enumerate(zip(embeddings, texts, metadata_list)):
                point_id = str(uuid.uuid4())
                point_ids.append(point_id)
                
                # Prepare payload with text and metadata
                payload = {
                    "text": text,
                    "document_id": metadata.get("document_id"),
                    "document_type": metadata.get("document_type"),
                    "chunk_index": metadata.get("chunk_index", i),
                    "token_count": metadata.get("token_count"),
                    "created_at": int(datetime.utcnow().timestamp()),
                    **metadata  # Include all additional metadata
                }
                
                points.append(
                    PointStruct(
                        id=point_id,
                        vector=embedding,
                        payload=payload
                    )
                )
            
            # Batch upsert points
            self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            
            logger.info(f"Stored {len(points)} embeddings in vector database")
            return point_ids
            
        except Exception as e:
            logger.error(f"Error storing embeddings: {e}")
            raise
    
    async def search_similar(
        self,
        query_embedding: List[float],
        limit: int = 10,
        score_threshold: float = 0.7,
        document_types: Optional[List[str]] = None,
        document_ids: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar embeddings.
        
        Args:
            query_embedding: Query vector
            limit: Maximum number of results
            score_threshold: Minimum similarity score
            document_types: Filter by document types
            document_ids: Filter by specific document IDs
            
        Returns:
            List of search results with text, metadata, and scores
        """
        try:
            # Build filter conditions
            filter_conditions = []
            
            if document_types:
                filter_conditions.append(
                    FieldCondition(
                        key="document_type",
                        match=MatchValue(any=document_types)
                    )
                )
            
            if document_ids:
                filter_conditions.append(
                    FieldCondition(
                        key="document_id",
                        match=MatchValue(any=document_ids)
                    )
                )
            
            # Create filter if conditions exist
            query_filter = None
            if filter_conditions:
                query_filter = Filter(must=filter_conditions)
            
            # Perform search
            search_results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                query_filter=query_filter,
                limit=limit,
                score_threshold=score_threshold
            )
            
            # Format results
            results = []
            for result in search_results:
                results.append({
                    "id": result.id,
                    "text": result.payload.get("text", ""),
                    "score": result.score,
                    "document_id": result.payload.get("document_id"),
                    "document_type": result.payload.get("document_type"),
                    "chunk_index": result.payload.get("chunk_index"),
                    "metadata": {k: v for k, v in result.payload.items() 
                              if k not in ["text", "document_id", "document_type", "chunk_index"]}
                })
            
            logger.info(f"Found {len(results)} similar documents")
            return results
            
        except Exception as e:
            logger.error(f"Error searching similar embeddings: {e}")
            raise
    
    async def delete_document_embeddings(self, document_id: str) -> bool:
        """
        Delete all embeddings for a specific document.
        
        Args:
            document_id: ID of the document to delete
            
        Returns:
            True if successful
        """
        try:
            # Delete points with matching document_id
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.FilterSelector(
                    filter=Filter(
                        must=[
                            FieldCondition(
                                key="document_id",
                                match=MatchValue(value=document_id)
                            )
                        ]
                    )
                )
            )
            
            logger.info(f"Deleted embeddings for document: {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document embeddings: {e}")
            return False
    
    async def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection."""
        try:
            info = self.client.get_collection(self.collection_name)
            return {
                "name": info.config.params.vectors.size,
                "vectors_count": info.vectors_count,
                "indexed_vectors_count": info.indexed_vectors_count,
                "points_count": info.points_count,
                "segments_count": info.segments_count,
                "status": info.status
            }
        except Exception as e:
            logger.error(f"Error getting collection info: {e}")
            return {}
    
    async def hybrid_search(
        self,
        query_embedding: List[float],
        query_text: str,
        limit: int = 10,
        semantic_weight: float = 0.7,
        keyword_weight: float = 0.3,
        document_types: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform hybrid search combining semantic and keyword search.
        
        Args:
            query_embedding: Query vector for semantic search
            query_text: Query text for keyword search
            limit: Maximum number of results
            semantic_weight: Weight for semantic search results
            keyword_weight: Weight for keyword search results
            document_types: Filter by document types
            
        Returns:
            Combined and ranked search results
        """
        try:
            # Perform semantic search
            semantic_results = await self.search_similar(
                query_embedding=query_embedding,
                limit=limit * 2,  # Get more results for reranking
                document_types=document_types
            )
            
            # Simple keyword matching (can be enhanced with full-text search)
            keyword_results = []
            query_terms = query_text.lower().split()
            
            for result in semantic_results:
                text_lower = result["text"].lower()
                keyword_score = sum(1 for term in query_terms if term in text_lower) / len(query_terms)
                
                if keyword_score > 0:
                    keyword_results.append({
                        **result,
                        "keyword_score": keyword_score
                    })
            
            # Combine scores
            combined_results = []
            for result in keyword_results:
                combined_score = (
                    semantic_weight * result["score"] +
                    keyword_weight * result.get("keyword_score", 0)
                )
                combined_results.append({
                    **result,
                    "combined_score": combined_score
                })
            
            # Sort by combined score and return top results
            combined_results.sort(key=lambda x: x["combined_score"], reverse=True)
            return combined_results[:limit]
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {e}")
            return await self.search_similar(query_embedding, limit, document_types=document_types)


# Global vector database service instance
vector_db_service = VectorDBService()
