"""
API routes for SCONIA application.
"""
from fastapi import APIRouter
from app.api.routes import chat, search, judges, constitution, admin

api_router = APIRouter()

# Include route modules
api_router.include_router(chat.router, prefix="/chat", tags=["chat"])
api_router.include_router(search.router, prefix="/search", tags=["search"])
api_router.include_router(judges.router, prefix="/judges", tags=["judges"])
api_router.include_router(constitution.router, prefix="/constitution", tags=["constitution"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
