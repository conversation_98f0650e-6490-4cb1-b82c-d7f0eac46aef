"""
Search API endpoints for SCONIA.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
import logging

from app.database import get_async_db
from app.services.rag import rag_service
from app.services.embeddings import embedding_service
from app.services.vector_db import vector_db_service

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/semantic")
async def semantic_search(
    query: str = Query(..., description="Search query"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results"),
    document_types: Optional[List[str]] = Query(None, description="Filter by document types"),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Perform semantic search across legal documents.
    """
    try:
        # Generate query embedding
        query_embedding = await embedding_service.generate_query_embedding(query)
        
        # Search vector database
        results = await vector_db_service.search_similar(
            query_embedding=query_embedding,
            limit=limit,
            document_types=document_types
        )
        
        return {
            "query": query,
            "results": results,
            "total_found": len(results)
        }
        
    except Exception as e:
        logger.error(f"Semantic search error: {e}")
        raise HTTPException(status_code=500, detail="Search failed")


@router.get("/hybrid")
async def hybrid_search(
    query: str = Query(..., description="Search query"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results"),
    semantic_weight: float = Query(0.7, ge=0.0, le=1.0, description="Weight for semantic search"),
    keyword_weight: float = Query(0.3, ge=0.0, le=1.0, description="Weight for keyword search"),
    document_types: Optional[List[str]] = Query(None, description="Filter by document types"),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Perform hybrid search combining semantic and keyword search.
    """
    try:
        # Generate query embedding
        query_embedding = await embedding_service.generate_query_embedding(query)
        
        # Perform hybrid search
        results = await vector_db_service.hybrid_search(
            query_embedding=query_embedding,
            query_text=query,
            limit=limit,
            semantic_weight=semantic_weight,
            keyword_weight=keyword_weight,
            document_types=document_types
        )
        
        return {
            "query": query,
            "search_type": "hybrid",
            "weights": {
                "semantic": semantic_weight,
                "keyword": keyword_weight
            },
            "results": results,
            "total_found": len(results)
        }
        
    except Exception as e:
        logger.error(f"Hybrid search error: {e}")
        raise HTTPException(status_code=500, detail="Search failed")


@router.get("/context")
async def get_search_context(
    query: str = Query(..., description="Search query"),
    max_results: int = Query(10, ge=1, le=20, description="Maximum results for context"),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Get search context using RAG pipeline (same as used in chat).
    """
    try:
        context, sources = await rag_service.retrieve_context(
            query=query,
            db=db,
            max_results=max_results
        )
        
        return {
            "query": query,
            "context": context,
            "sources": sources,
            "source_count": len(sources)
        }
        
    except Exception as e:
        logger.error(f"Context retrieval error: {e}")
        raise HTTPException(status_code=500, detail="Context retrieval failed")


@router.get("/suggestions")
async def get_search_suggestions(
    query: str = Query(..., description="Partial query for suggestions"),
    limit: int = Query(5, ge=1, le=10, description="Maximum number of suggestions")
):
    """
    Get search suggestions based on partial query.
    """
    try:
        # Simple suggestion logic - can be enhanced with ML
        suggestions = []
        
        query_lower = query.lower()
        
        # Constitutional suggestions
        if any(term in query_lower for term in ['const', 'right', 'fund']):
            suggestions.extend([
                "fundamental rights in Nigerian Constitution",
                "constitutional provisions on freedom",
                "Chapter IV fundamental rights"
            ])
        
        # Judge suggestions
        if any(term in query_lower for term in ['judge', 'justice', 'cjn']):
            suggestions.extend([
                "current Supreme Court justices",
                "Chief Justice of Nigeria",
                "Supreme Court judges appointment"
            ])
        
        # Case law suggestions
        if any(term in query_lower for term in ['case', 'precedent', 'judgment']):
            suggestions.extend([
                "landmark Supreme Court cases",
                "recent court judgments",
                "legal precedents Nigeria"
            ])
        
        # Procedure suggestions
        if any(term in query_lower for term in ['file', 'procedure', 'how']):
            suggestions.extend([
                "how to file an appeal",
                "court filing procedures",
                "Supreme Court filing requirements"
            ])
        
        # Fee suggestions
        if any(term in query_lower for term in ['fee', 'cost', 'pay']):
            suggestions.extend([
                "Supreme Court filing fees",
                "court fees schedule",
                "payment methods for court fees"
            ])
        
        # Remove duplicates and limit
        unique_suggestions = list(dict.fromkeys(suggestions))[:limit]
        
        return {
            "query": query,
            "suggestions": unique_suggestions
        }
        
    except Exception as e:
        logger.error(f"Suggestions error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get suggestions")


@router.get("/stats")
async def get_search_stats(db: AsyncSession = Depends(get_async_db)):
    """
    Get search and vector database statistics.
    """
    try:
        # Get vector database info
        vector_info = await vector_db_service.get_collection_info()
        
        # Get embedding service stats
        embedding_stats = embedding_service.get_cache_stats()
        
        return {
            "vector_database": vector_info,
            "embedding_cache": embedding_stats,
            "status": "operational"
        }
        
    except Exception as e:
        logger.error(f"Stats error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get statistics")
