"""
Chat API endpoints for SCONIA.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
import json
import logging

from app.database import get_async_db
from app.schemas.chat import ChatR<PERSON><PERSON>, ChatResponse, ChatSession
from app.services.chat import ChatService
from app.services.auth import get_current_user_optional

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/", response_model=ChatResponse)
async def chat_endpoint(
    request: ChatRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user = Depends(get_current_user_optional)
):
    """
    Main chat endpoint for SCONIA.
    Processes user queries and returns AI-generated responses with legal information.
    """
    try:
        chat_service = ChatService(db)
        response = await chat_service.process_query(
            query=request.query,
            session_id=request.session_id,
            context=request.context,
            user_id=getattr(current_user, 'id', None)
        )
        return response
    except Exception as e:
        logger.error(f"Chat endpoint error: {e}")
        raise HTTPException(status_code=500, detail="Failed to process query")


@router.get("/sessions/{session_id}", response_model=ChatSession)
async def get_chat_session(
    session_id: str,
    db: AsyncSession = Depends(get_async_db)
):
    """Get chat session history."""
    try:
        chat_service = ChatService(db)
        session = await chat_service.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        return session
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get session error: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve session")


@router.post("/feedback")
async def submit_feedback(
    session_id: str,
    query_id: Optional[int] = None,
    rating: Optional[int] = None,
    feedback_text: Optional[str] = None,
    db: AsyncSession = Depends(get_async_db)
):
    """Submit user feedback for a chat response."""
    try:
        chat_service = ChatService(db)
        await chat_service.submit_feedback(
            session_id=session_id,
            query_id=query_id,
            rating=rating,
            feedback_text=feedback_text
        )
        return {"message": "Feedback submitted successfully"}
    except Exception as e:
        logger.error(f"Feedback submission error: {e}")
        raise HTTPException(status_code=500, detail="Failed to submit feedback")


@router.websocket("/ws/{session_id}")
async def websocket_chat(websocket: WebSocket, session_id: str):
    """
    WebSocket endpoint for real-time chat.
    Provides streaming responses and typing indicators.
    """
    await websocket.accept()
    
    try:
        # Get database session
        async with get_async_db() as db:
            chat_service = ChatService(db)
            
            while True:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                if message.get("type") == "query":
                    # Process query and stream response
                    query = message.get("query", "")
                    
                    # Send typing indicator
                    await websocket.send_text(json.dumps({
                        "type": "typing",
                        "status": "thinking"
                    }))
                    
                    # Process query
                    response = await chat_service.process_query_streaming(
                        query=query,
                        session_id=session_id,
                        websocket=websocket
                    )
                    
                    # Send final response
                    await websocket.send_text(json.dumps({
                        "type": "response",
                        "data": response.dict()
                    }))
                
                elif message.get("type") == "ping":
                    # Respond to ping
                    await websocket.send_text(json.dumps({
                        "type": "pong"
                    }))
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for session {session_id}")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": "An error occurred"
        }))
        await websocket.close()
