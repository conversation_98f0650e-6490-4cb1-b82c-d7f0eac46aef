# Database Configuration
DATABASE_URL=postgresql://sconia_user:sconia_password@localhost:5432/sconia_db
DATABASE_URL_ASYNC=postgresql+asyncpg://sconia_user:sconia_password@localhost:5432/sconia_db

# Vector Database Configuration
VECTOR_DB_TYPE=qdrant  # Options: qdrant, chromadb
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your_qdrant_api_key_here
CHROMADB_PATH=./data/chromadb

# AI/ML Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
EMBEDDING_MODEL=text-embedding-ada-002
MAX_TOKENS=4000
TEMPERATURE=0.1

# Application Configuration
APP_NAME=SCONIA
APP_VERSION=1.0.0
DEBUG=True
SECRET_KEY=your_super_secret_key_here_change_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8000"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=./uploads
ALLOWED_FILE_TYPES=["pdf", "docx", "txt"]

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379/0

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Content Moderation
ENABLE_CONTENT_MODERATION=True
PROFANITY_FILTER=True

# Analytics and Monitoring
ENABLE_ANALYTICS=True
PROMETHEUS_PORT=8001
