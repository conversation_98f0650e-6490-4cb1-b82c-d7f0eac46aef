# SCONIA Environment Variables Setup Guide

## 🔧 **Required Environment Variables**

### **1. Copy the Example File**
```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your actual values
nano .env  # or use your preferred editor
```

## 🔑 **Critical Variables You MUST Set**

### **1. OpenAI API Key (REQUIRED)**
```bash
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-actual-openai-api-key-here

# AI Model Configuration
OPENAI_MODEL=gpt-4                    # or gpt-3.5-turbo for cheaper option
EMBEDDING_MODEL=text-embedding-ada-002
MAX_TOKENS=4000
TEMPERATURE=0.1
```

**How to get OpenAI API Key:**
1. Go to https://platform.openai.com/api-keys
2. Sign up/login to OpenAI
3. Create a new API key
4. Copy the key (starts with `sk-`)
5. Add billing information (required for API usage)

### **2. Database Configuration**
```bash
# PostgreSQL Database (Docker will create this automatically)
DATABASE_URL=postgresql://sconia_user:sconia_password@localhost:5432/sconia_db
DATABASE_URL_ASYNC=postgresql+asyncpg://sconia_user:sconia_password@localhost:5432/sconia_db
```

### **3. Vector Database (Qdrant)**
```bash
# Qdrant Vector Database (Docker will run this)
VECTOR_DB_TYPE=qdrant
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=                       # Leave empty for local Docker instance
```

### **4. Security Configuration**
```bash
# Generate a secure secret key
SECRET_KEY=your_super_secret_key_here_change_in_production_make_it_long_and_random
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

**Generate a secure secret key:**
```bash
# Generate a random secret key
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

## 📋 **Complete .env File Template**

Here's your complete `.env` file with explanations:

```bash
# =============================================================================
# SCONIA Environment Configuration
# =============================================================================

# -----------------------------------------------------------------------------
# AI/ML Configuration (REQUIRED)
# -----------------------------------------------------------------------------
OPENAI_API_KEY=sk-your-actual-openai-api-key-here
OPENAI_MODEL=gpt-4
EMBEDDING_MODEL=text-embedding-ada-002
MAX_TOKENS=4000
TEMPERATURE=0.1

# -----------------------------------------------------------------------------
# Database Configuration (Auto-configured with Docker)
# -----------------------------------------------------------------------------
DATABASE_URL=postgresql://sconia_user:sconia_password@localhost:5432/sconia_db
DATABASE_URL_ASYNC=postgresql+asyncpg://sconia_user:sconia_password@localhost:5432/sconia_db

# -----------------------------------------------------------------------------
# Vector Database Configuration (Auto-configured with Docker)
# -----------------------------------------------------------------------------
VECTOR_DB_TYPE=qdrant
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=

# -----------------------------------------------------------------------------
# Redis Configuration (Auto-configured with Docker)
# -----------------------------------------------------------------------------
REDIS_URL=redis://localhost:6379/0

# -----------------------------------------------------------------------------
# Application Configuration
# -----------------------------------------------------------------------------
APP_NAME=SCONIA
APP_VERSION=1.0.0
DEBUG=True
SECRET_KEY=generate-a-secure-random-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# -----------------------------------------------------------------------------
# CORS Configuration (for frontend)
# -----------------------------------------------------------------------------
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://localhost:5173"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# -----------------------------------------------------------------------------
# File Upload Configuration
# -----------------------------------------------------------------------------
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads
ALLOWED_FILE_TYPES=["pdf", "docx", "txt", "html"]

# -----------------------------------------------------------------------------
# Performance & Monitoring
# -----------------------------------------------------------------------------
LOG_LEVEL=INFO
RATE_LIMIT_PER_MINUTE=60
ENABLE_ANALYTICS=True

# -----------------------------------------------------------------------------
# Content Moderation
# -----------------------------------------------------------------------------
ENABLE_CONTENT_MODERATION=True
PROFANITY_FILTER=True
```

## 🚀 **Quick Setup Commands**

### **1. Set Up Environment**
```bash
# 1. Copy environment file
cp .env.example .env

# 2. Generate secret key
echo "SECRET_KEY=$(python -c 'import secrets; print(secrets.token_urlsafe(32))')" >> .env

# 3. Edit .env file to add your OpenAI API key
nano .env
# Add your OpenAI API key to the OPENAI_API_KEY line
```

### **2. Verify Environment**
```bash
# Check if environment variables are loaded
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('OpenAI Key:', 'SET' if os.getenv('OPENAI_API_KEY') else 'NOT SET')
print('Database URL:', 'SET' if os.getenv('DATABASE_URL') else 'NOT SET')
print('Vector DB URL:', 'SET' if os.getenv('QDRANT_URL') else 'NOT SET')
"
```

## 🔍 **Environment Variable Explanations**

### **AI/ML Variables:**
- `OPENAI_API_KEY`: Your OpenAI API key for GPT and embeddings
- `OPENAI_MODEL`: Which GPT model to use (gpt-4 or gpt-3.5-turbo)
- `EMBEDDING_MODEL`: Model for generating text embeddings
- `MAX_TOKENS`: Maximum tokens per AI response
- `TEMPERATURE`: AI creativity (0.1 = focused, 1.0 = creative)

### **Database Variables:**
- `DATABASE_URL`: PostgreSQL connection for main database
- `DATABASE_URL_ASYNC`: Async PostgreSQL connection
- `QDRANT_URL`: Vector database URL
- `REDIS_URL`: Redis cache connection

### **Security Variables:**
- `SECRET_KEY`: JWT token signing key (keep secret!)
- `ALGORITHM`: JWT algorithm
- `ACCESS_TOKEN_EXPIRE_MINUTES`: How long tokens last

### **Performance Variables:**
- `RATE_LIMIT_PER_MINUTE`: API requests per minute per user
- `MAX_FILE_SIZE`: Maximum upload file size
- `LOG_LEVEL`: Logging verbosity

## ⚠️ **Common Issues & Solutions**

### **Issue: OpenAI API Errors**
```bash
# Check if API key is set
echo $OPENAI_API_KEY

# Test API key
curl https://api.openai.com/v1/models \
  -H "Authorization: Bearer $OPENAI_API_KEY"
```

### **Issue: Database Connection Errors**
```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Check database URL
echo $DATABASE_URL
```

### **Issue: Vector Database Errors**
```bash
# Check if Qdrant is running
docker-compose ps qdrant

# Test Qdrant connection
curl http://localhost:6333/collections
```

## 🎯 **Minimal Working Configuration**

**For testing, you only need:**

```bash
# Minimum required variables
OPENAI_API_KEY=sk-your-actual-key-here
SECRET_KEY=any-random-string-here
DEBUG=True

# Everything else can use defaults from .env.example
```

## 🔐 **Production Security Notes**

**For production deployment:**

1. **Change DEBUG to False**
2. **Use a strong SECRET_KEY**
3. **Set specific ALLOWED_ORIGINS**
4. **Use environment-specific database URLs**
5. **Enable HTTPS**
6. **Set up proper logging**

## 📝 **Environment Checklist**

Before running SCONIA, verify:

- [ ] ✅ `.env` file exists
- [ ] ✅ `OPENAI_API_KEY` is set with valid key
- [ ] ✅ `SECRET_KEY` is set with random string
- [ ] ✅ Database URLs point to correct instances
- [ ] ✅ Vector database URL is correct
- [ ] ✅ File upload directory exists
- [ ] ✅ All required services are in docker-compose.yml

## 🚀 **Test Your Environment**

```bash
# Test environment setup
python scripts/quick_test.py

# Or test manually
curl http://localhost:8000/health
```

**If everything is configured correctly, you should see:**
```json
{
  "status": "healthy",
  "app_name": "SCONIA",
  "version": "1.0.0"
}
```

## 💡 **Pro Tips**

1. **Keep your OpenAI API key secure** - never commit it to git
2. **Use different .env files** for development/staging/production
3. **Monitor your OpenAI usage** to avoid unexpected bills
4. **Backup your SECRET_KEY** - changing it invalidates all tokens
5. **Test with gpt-3.5-turbo first** - it's cheaper than gpt-4

**Your environment is ready when all services start without errors and the health check passes!** 🎉
