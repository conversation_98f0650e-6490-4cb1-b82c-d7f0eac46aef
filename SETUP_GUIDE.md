# SCONIA Setup Guide

Complete setup guide for SCONIA (Supreme Court of Nigeria Information Assistant).

## Prerequisites

- Dock<PERSON> and Docker Compose
- Python 3.11+ (for local development)
- OpenAI API key
- At least 4GB RAM and 10GB disk space

## Quick Start (Recommended)

### 1. Environment Setup
```bash
# Clone the repository
git clone <repository-url>
cd sconia

# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

**Required Environment Variables:**
```bash
# OpenAI API Key (REQUIRED)
OPENAI_API_KEY=your_openai_api_key_here

# Database URLs (auto-configured for Docker)
DATABASE_URL=postgresql://sconia_user:sconia_password@localhost:5432/sconia_db
DATABASE_URL_ASYNC=postgresql+asyncpg://sconia_user:sconia_password@localhost:5432/sconia_db

# Vector Database
QDRANT_URL=http://localhost:6333

# Security
SECRET_KEY=your_super_secret_key_here_change_in_production
```

### 2. Start Services
```bash
# Start all services (PostgreSQL, Qdrant, Redis, API)
docker-compose up -d

# Check service status
docker-compose ps

# View API logs
docker-compose logs -f api
```

### 3. Initialize Database
```bash
# Run database migrations
docker-compose exec api alembic upgrade head

# Initialize with sample data
docker-compose exec api python scripts/init_data.py

# Initialize vector database with legal documents
docker-compose exec api python scripts/init_vector_db.py
```

### 4. Test the System
```bash
# Health check
curl http://localhost:8000/health

# Test chat endpoint
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What are the fundamental rights in the Nigerian Constitution?",
    "session_id": "test-session-123"
  }'
```

## Access Points

- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Chat Endpoint**: http://localhost:8000/api/v1/chat
- **Search Endpoint**: http://localhost:8000/api/v1/search
- **Judges Info**: http://localhost:8000/api/v1/judges
- **Constitution**: http://localhost:8000/api/v1/constitution

## Local Development Setup

### 1. Python Environment
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install spaCy model for NLP
python -m spacy download en_core_web_sm
```

### 2. Start Database Services Only
```bash
# Start only database services
docker-compose up -d postgres qdrant redis

# Wait for services to be ready
sleep 10
```

### 3. Run Migrations and Initialize Data
```bash
# Set environment variables
export DATABASE_URL="postgresql://sconia_user:sconia_password@localhost:5432/sconia_db"
export OPENAI_API_KEY="your_api_key_here"

# Run migrations
alembic upgrade head

# Initialize data
python scripts/init_data.py
python scripts/init_vector_db.py
```

### 4. Start Development Server
```bash
# Start FastAPI development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## Testing the AI Features

### 1. Basic Chat Queries
```bash
# Constitutional query
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Content-Type: application/json" \
  -d '{"query": "What is Section 33 of the Nigerian Constitution?"}'

# Judge information
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Content-Type: application/json" \
  -d '{"query": "Who is the current Chief Justice of Nigeria?"}'

# Procedural query
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Content-Type: application/json" \
  -d '{"query": "How do I file an appeal to the Supreme Court?"}'
```

### 2. WebSocket Chat
```javascript
const ws = new WebSocket('ws://localhost:8000/api/v1/chat/ws/session-123');

ws.onopen = function() {
    ws.send(JSON.stringify({
        type: 'query',
        query: 'Tell me about fundamental rights in Nigeria'
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Response:', data);
};
```

### 3. Search Endpoints
```bash
# Semantic search
curl "http://localhost:8000/api/v1/search/semantic?query=fundamental%20rights&limit=5"

# Hybrid search
curl "http://localhost:8000/api/v1/search/hybrid?query=Supreme%20Court&limit=5"

# Get search context (RAG pipeline)
curl "http://localhost:8000/api/v1/search/context?query=Chief%20Justice"
```

## Adding Legal Documents

### 1. Via API (Admin Required)
```bash
# Upload document (requires admin authentication)
curl -X POST "http://localhost:8000/api/v1/admin/documents/upload" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@legal_document.pdf" \
  -F "document_type=case"
```

### 2. Via Script
```python
# Add to scripts/add_document.py
from app.services.document_processor import document_processor

async def add_document():
    document_id = await document_processor.process_document(
        file_path="path/to/document.pdf",
        document_type="constitution",  # or "case", "procedure", etc.
        db=db_session,
        use_openai=True
    )
    print(f"Document processed: {document_id}")
```

## Configuration Options

### AI/ML Settings
```bash
# Use local models instead of OpenAI
OPENAI_API_KEY=""  # Leave empty to use local models

# Adjust AI parameters
OPENAI_MODEL=gpt-4
TEMPERATURE=0.1
MAX_TOKENS=4000
```

### Vector Database Settings
```bash
# Use ChromaDB instead of Qdrant
VECTOR_DB_TYPE=chromadb
CHROMADB_PATH=./data/chromadb

# Qdrant settings
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your_qdrant_api_key
```

### Performance Tuning
```bash
# Rate limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Caching
REDIS_URL=redis://localhost:6379/0

# File upload limits
MAX_FILE_SIZE=10485760  # 10MB
```

## Troubleshooting

### Common Issues

1. **OpenAI API Errors**
   - Check API key is valid
   - Verify sufficient credits
   - Check rate limits

2. **Vector Database Connection**
   - Ensure Qdrant is running: `docker-compose ps`
   - Check Qdrant logs: `docker-compose logs qdrant`
   - Verify collection exists: `curl http://localhost:6333/collections`

3. **Database Connection**
   - Check PostgreSQL is running
   - Verify connection string
   - Run migrations: `alembic upgrade head`

4. **Memory Issues**
   - Increase Docker memory limits
   - Use local models instead of OpenAI
   - Reduce batch sizes in processing

### Logs and Debugging
```bash
# View all service logs
docker-compose logs

# View specific service logs
docker-compose logs api
docker-compose logs postgres
docker-compose logs qdrant

# Enable debug mode
export DEBUG=True
```

## Production Deployment

### Security Checklist
- [ ] Change default passwords
- [ ] Set strong SECRET_KEY
- [ ] Enable HTTPS/TLS
- [ ] Configure firewall rules
- [ ] Set up monitoring
- [ ] Enable backup systems

### Performance Optimization
- [ ] Use production WSGI server (Gunicorn)
- [ ] Configure load balancing
- [ ] Set up CDN for static assets
- [ ] Optimize database queries
- [ ] Configure caching layers

### Monitoring
- [ ] Set up health checks
- [ ] Configure log aggregation
- [ ] Monitor API response times
- [ ] Track usage analytics
- [ ] Set up alerting

## Support

For issues and questions:
- Check the logs first
- Review this setup guide
- Create an issue on GitHub
- Check API documentation at `/docs`

## Next Steps

After successful setup:
1. Test all API endpoints
2. Upload additional legal documents
3. Configure frontend application
4. Set up production deployment
5. Implement monitoring and analytics
