# SCONIA Testing Guide

Complete testing guide for all SCONIA components built so far.

## Prerequisites

### 1. Required Tools
```bash
# Install testing tools
npm install -g wscat  # For WebSocket testing
pip install httpx pytest pytest-asyncio  # For Python testing

# Or use curl (usually pre-installed)
curl --version
```

### 2. Environment Setup
```bash
# Make sure you have your .env file configured
cp .env.example .env
# Edit .env with your OpenAI API key and other settings

# Verify environment variables
echo $OPENAI_API_KEY  # Should show your API key
```

## Quick Health Check

### 1. Start All Services
```bash
# Start the complete system
docker-compose up -d

# Check all services are running
docker-compose ps

# Expected output: All services should be "Up"
# - postgres (database)
# - qdrant (vector database)  
# - redis (caching)
# - api (SCONIA API)
```

### 2. Basic Health Check
```bash
# Test basic API health
curl http://localhost:8000/health

# Expected response:
# {
#   "status": "healthy",
#   "app_name": "SCON<PERSON>",
#   "version": "1.0.0",
#   "timestamp": "2024-01-01T00:00:00Z"
# }
```

## Phase 1: Core System Testing

### 1. Database Connectivity
```bash
# Test database connection
curl http://localhost:8000/api/v1/admin/system/status

# Expected: Should return system status without database errors
```

### 2. Vector Database (Qdrant) Testing
```bash
# Check Qdrant is running
curl http://localhost:6333/collections

# Test vector database through API
curl http://localhost:8000/api/v1/search/stats

# Expected: Should return vector database info
```

### 3. Initialize Sample Data
```bash
# Initialize database with sample data
docker-compose exec api python scripts/init_data.py

# Initialize vector database with legal documents
docker-compose exec api python scripts/init_vector_db.py

# Expected: Should complete without errors and show processed documents
```

## Phase 2: AI/ML Pipeline Testing

### 1. Test Embedding Generation
```bash
# Test embedding service
curl -X POST "http://localhost:8000/api/v1/search/semantic" \
  -H "Content-Type: application/json" \
  -d '{"query": "fundamental rights", "limit": 3}'

# Expected response:
# {
#   "query": "fundamental rights",
#   "results": [...],
#   "total_found": 3
# }
```

### 2. Test Vector Search
```bash
# Test semantic search
curl "http://localhost:8000/api/v1/search/semantic?query=Chief%20Justice&limit=5"

# Expected: Should return relevant legal documents
```

### 3. Test RAG Pipeline
```bash
# Test context retrieval
curl "http://localhost:8000/api/v1/search/context?query=constitutional%20rights"

# Expected: Should return context and sources
```

## Phase 3: API Endpoint Testing

### 1. Chat API Testing
```bash
# Test basic chat
curl -X POST "http://localhost:8000/api/v1/chat/" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What are fundamental rights in Nigerian Constitution?",
    "session_id": "test-session-123"
  }'

# Expected: Should return AI response with sources and quick options
```

### 2. Legal Content APIs
```bash
# Test judges API
curl "http://localhost:8000/api/v1/judges/"

# Test constitution API
curl "http://localhost:8000/api/v1/constitution/"

# Test cases API
curl "http://localhost:8000/api/v1/cases/"

# Test procedures API
curl "http://localhost:8000/api/v1/procedures/"

# Test fees API
curl "http://localhost:8000/api/v1/fees/"

# Expected: Each should return relevant legal data
```

### 3. Search APIs
```bash
# Test faceted search
curl "http://localhost:8000/api/v1/search/faceted?query=rights&limit=5"

# Test search suggestions
curl "http://localhost:8000/api/v1/search/suggestions?query=const"

# Test search filters
curl "http://localhost:8000/api/v1/search/filters"

# Expected: Should return search results and suggestions
```

## Phase 4: WebSocket Testing

### 1. Test WebSocket Connection
```bash
# Connect to WebSocket (requires wscat)
wscat -c ws://localhost:8000/api/v1/chat/ws/test-session

# You should see connection established message:
# {"type": "connection_established", "session_id": "test-session", ...}
```

### 2. Test WebSocket Chat
```bash
# In the wscat session, send:
{"type": "query", "query": "Tell me about the Supreme Court of Nigeria"}

# Expected: Should receive progress updates and final response
```

### 3. Test WebSocket Features
```bash
# Test ping/pong
{"type": "ping"}

# Test typing indicator
{"type": "typing", "is_typing": true}

# Test feedback
{"type": "feedback", "rating": 5, "feedback_text": "Great response!"}
```

## Phase 5: Advanced Features Testing

### 1. File Upload Testing (Requires Admin Token)
```bash
# First, get admin token (you'll need to create admin user)
# Create a test file
echo "This is a test legal document for SCONIA." > test_document.txt

# Test file validation
curl -X POST "http://localhost:8000/api/v1/admin/documents/validate" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -F "file=@test_document.txt" \
  -F "document_type=general"

# Test file upload
curl -X POST "http://localhost:8000/api/v1/admin/documents/upload" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -F "file=@test_document.txt" \
  -F "document_type=general"
```

### 2. Monitoring & Performance Testing
```bash
# Test system health
curl "http://localhost:8000/api/v1/monitoring/health"

# Test WebSocket stats (requires admin token)
curl "http://localhost:8000/api/v1/websocket/stats" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Test performance metrics
curl "http://localhost:8000/api/v1/monitoring/metrics/system" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## Phase 6: Automated Testing

### 1. Run Test Suite
```bash
# Run comprehensive test suite
python scripts/run_tests.py

# Or run specific tests
python -m pytest tests/test_api_endpoints.py -v
python -m pytest tests/test_websocket.py -v
```

### 2. Load Testing (Optional)
```bash
# Simple load test with curl
for i in {1..10}; do
  curl -s "http://localhost:8000/health" &
done
wait

# Check if all requests succeeded
```

## Expected Results Summary

### ✅ What Should Work:
1. **Health Check**: Returns healthy status
2. **Chat API**: Responds to queries with AI-generated answers
3. **Search APIs**: Returns relevant legal documents
4. **WebSocket**: Real-time chat with streaming responses
5. **Legal Content APIs**: Returns judges, constitution, cases, etc.
6. **Vector Search**: Semantic search across legal documents
7. **Monitoring**: System metrics and health status

### ❌ Common Issues & Solutions:

**Issue**: `Connection refused` errors
**Solution**: 
```bash
# Check if services are running
docker-compose ps
# Restart if needed
docker-compose restart
```

**Issue**: `OpenAI API errors`
**Solution**:
```bash
# Check API key is set
echo $OPENAI_API_KEY
# Update .env file with valid key
```

**Issue**: `Vector database errors`
**Solution**:
```bash
# Reinitialize vector database
docker-compose exec api python scripts/init_vector_db.py
```

**Issue**: `Database connection errors`
**Solution**:
```bash
# Check database is running
docker-compose logs postgres
# Run migrations
docker-compose exec api alembic upgrade head
```

## Performance Benchmarks

### Expected Response Times:
- **Health Check**: < 100ms
- **Chat Query**: 2-5 seconds (depending on OpenAI)
- **Search Query**: < 1 second
- **WebSocket Connection**: < 500ms
- **API Endpoints**: < 500ms

### Expected Throughput:
- **Concurrent WebSocket Connections**: 100+
- **API Requests**: 60/minute per client
- **File Upload**: Up to 10MB files
- **Search Results**: 10-20 results per query

## Next Steps

After all tests pass:
1. ✅ **System is ready for frontend development**
2. ✅ **All APIs are functional and tested**
3. ✅ **WebSocket real-time features working**
4. ✅ **AI/ML pipeline operational**
5. ✅ **Ready for Phase 4: Frontend Development**

## Troubleshooting Commands

```bash
# View API logs
docker-compose logs api

# View all service logs
docker-compose logs

# Restart specific service
docker-compose restart api

# Rebuild and restart
docker-compose down
docker-compose up --build -d

# Check resource usage
docker stats

# Access API container
docker-compose exec api bash
```
